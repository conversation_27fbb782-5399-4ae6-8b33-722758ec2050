#set($page_title='页面标题')

#parse("database/execute/database_execute_result.vm")

<style>
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
        <el-form-item label="id">
            <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
        </el-form-item>
        <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
        <el-form-item>
            <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
            <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column prop="id" label="id"></el-table-column>
        <el-table-column prop="name" label="name"></el-table-column>
        <el-table-column prop="databaseId" label="databaseId"></el-table-column>
        <el-table-column prop="databaseName" label="databaseName"></el-table-column>
        <el-table-column prop="status" label="status"></el-table-column>
        <el-table-column prop="sql" label="sql"></el-table-column>
        <el-table-column prop="keyStart" label="keyStart"></el-table-column>
        <el-table-column prop="keyEnd" label="keyEnd"></el-table-column>
        <el-table-column prop="batchOffset" label="batchOffset"></el-table-column>
        <el-table-column prop="currentKeyValue" label="currentKeyValue"></el-table-column>
        <el-table-column prop="lastExecuteTime" label="lastExecuteTime"></el-table-column>
        <el-table-column prop="createTime" label="createTime"></el-table-column>
        <el-table-column prop="updateTime" label="updateTime"></el-table-column>
        <el-table-column prop="createUserId" label="createUserId"></el-table-column>
        <el-table-column prop="updateUserId" label="updateUserId"></el-table-column>
        <el-table-column label="操作">
            <template slot-scope="scope">
                <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
            </template>
        </el-table-column>
    </el-table>

    <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>

    <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false">
        <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
            <el-form-item label="name" prop="name">
                <el-input v-model="addEditForm.name" placeholder="执行任务名称"></el-input>
            </el-form-item>
            <el-form-item label="databaseId" prop="databaseId">
                <el-select v-model="addEditForm.databaseId" placeholder="请选择数据库">
                    <el-option v-for="item in databases" :key="item.databaseId" :label="item.name" :value="item.databaseId"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="databaseName" prop="databaseName">
                <el-input v-model="addEditForm.databaseName" placeholder="数据库名,允许为空"></el-input>
            </el-form-item>
            <el-form-item label="sql" prop="sql">
                <el-input type="textarea" :rows="5" v-model="addEditForm.sql" placeholder="执行的SQL，该sql中应该包含:keyStart 和 :keyEnd 两个变量占位符，用于每一批次执行时确定范围"></el-input>
            </el-form-item>
            <el-form-item label="keyStart" prop="keyStart">
                <el-input v-model="addEditForm.keyStart" placeholder="开始的主键，包含；当它是数字时，程序会自动按数字处理；同时也支持非数字类型的key"></el-input>
            </el-form-item>
            <el-form-item label="keyEnd" prop="keyEnd">
                <el-input v-model="addEditForm.keyEnd" placeholder="结束的主键，包含；当它是数字时，程序会自动按数字处理；同时也支持非数字类型的key"></el-input>
            </el-form-item>
            <el-form-item label="batchOffset" prop="batchOffset">
                <el-input v-model="addEditForm.batchOffset" placeholder="每次执行的偏移量，即每次执行范围=[当前key,当前key+batch_offset)"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
            <el-button @click="showDialog = false">取消</el-button>
            <el-button type="primary" @click="doAddOrEdit">确定</el-button>
        </div>
    </el-dialog>

</div>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    var vm = new Vue({
        el: '#app',
        data: {
            queryForm: Utils.copy(defaultQueryForm),
            addEditForm: Utils.copy(defaultAddForm),
            rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
            total: 0, tableData: [], tableLoading: false,
            showDialog: false, dialogTitle: '',
            databases: []
        },
        created: function() {
            this.getData()
            var that = this
            Resource.get("${_contextPath_}/database/get_database_for_select", {}, function(resp){
                that.databases = resp.data
            })
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                Resource.get("${_contextPath_}/database_execute_config/get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/database_execute_config/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增数据库执行任务配置表' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/database_execute_config/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            }
        }
    })
</script>
